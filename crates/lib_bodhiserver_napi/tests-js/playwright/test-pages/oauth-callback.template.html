<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Callback</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .status { margin: 20px 0; padding: 15px; border-radius: 4px; }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #e7f3ff; color: #004085; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 20px auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        button { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>OAuth Callback Processing</h1>
        
        <div id="loading">
            <div class="spinner"></div>
            <p>Processing OAuth callback...</p>
        </div>

        <div id="status" class="status" style="display: none;"></div>
        
        <div id="actions" style="display: none;">
            <button onclick="goHome()">Back to Test Page</button>
        </div>
    </div>

    <script>
        async function processCallback() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                const state = urlParams.get('state');
                const error = urlParams.get('error');

                if (error) {
                    throw new Error('OAuth error: ' + error + ' - ' + (urlParams.get('error_description') || 'Unknown error'));
                }

                if (!code) {
                    throw new Error('No authorization code received');
                }

                const storedState = localStorage.getItem('oauth_state');
                if (!storedState || storedState !== state) {
                    throw new Error('Invalid state parameter - possible CSRF attack');
                }

                const codeVerifier = localStorage.getItem('oauth_code_verifier');
                if (!codeVerifier) {
                    throw new Error('No code verifier found in storage');
                }

                showStatus('Exchanging authorization code for token...', 'info');

                const tokenResponse = await exchangeCodeForToken(code, codeVerifier);

                if (tokenResponse.access_token) {
                    localStorage.setItem('access_token', tokenResponse.access_token);
                    if (tokenResponse.refresh_token) {
                        localStorage.setItem('refresh_token', tokenResponse.refresh_token);
                    }

                    showStatus('✅ Token received successfully! This token will be exchanged by our server.', 'success');
                    
                    localStorage.removeItem('oauth_state');
                    localStorage.removeItem('oauth_code_verifier');
                    localStorage.removeItem('oauth_timestamp');

                    document.getElementById('actions').style.display = 'block';
                } else {
                    throw new Error('No access token in response');
                }

            } catch (error) {
                console.error('Callback processing error:', error);
                showStatus('❌ Error: ' + error.message, 'error');
                document.getElementById('actions').style.display = 'block';
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        async function exchangeCodeForToken(code, codeVerifier) {
            const tokenUrl = '{{AUTH_URL}}/realms/{{AUTH_REALM}}/protocol/openid-connect/token';
            
            const params = new URLSearchParams({
                grant_type: 'authorization_code',
                client_id: '{{THIRD_PARTY_CLIENT_ID}}',
                client_secret: '{{THIRD_PARTY_CLIENT_SECRET}}',
                code: code,
                redirect_uri: window.location.origin + '/auth/callback/',
                code_verifier: codeVerifier
            });

            const response = await fetch(tokenUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            });

            const responseText = await response.text();

            if (!response.ok) {
                throw new Error('Token exchange failed: ' + response.status + ' - ' + responseText);
            }

            return JSON.parse(responseText);
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
        }

        function goHome() {
            window.location.href = '../../index.html';
        }

        document.addEventListener('DOMContentLoaded', processCallback);
    </script>
</body>
</html>

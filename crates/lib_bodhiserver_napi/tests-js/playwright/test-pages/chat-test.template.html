<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .info { background-color: #e7f3ff; color: #004085; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin: 10px 0; }
        button:hover { background-color: #0056b3; }
        input[type="text"] { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        #response { margin: 15px 0; padding: 10px; border-radius: 4px; background-color: #f8f9fa; border: 1px solid #dee2e6; min-height: 50px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Chat API Test</h1>
        
        <div class="section info">
            <h3>Test Configuration</h3>
            <p><strong>Server URL:</strong> {{SERVER_URL}}</p>
            <p><strong>Token Status:</strong> <span id="tokenStatus">Checking...</span></p>
        </div>

        <div class="section">
            <h3>Chat Completions Test</h3>
            <p>Test the chat completions API with the exchanged OAuth token.</p>
            
            <label for="modelInput">Model:</label>
            <input type="text" id="modelInput" value="gpt-3.5-turbo" placeholder="Model name">
            
            <label for="messageInput">Message:</label>
            <input type="text" id="messageInput" placeholder="Enter your message">
            
            <button id="sendBtn" onclick="sendChatMessage()">Send Message</button>
            
            <div id="response">No response yet...</div>
        </div>

        <div class="section">
            <h3>Actions</h3>
            <button onclick="goBack()" style="background-color: #6c757d;">Back to OAuth Test</button>
            <button onclick="clearStorage()" style="background-color: #dc3545;">Clear Storage</button>
        </div>
    </div>

    <script>
        // Configuration
        const CONFIG = {
            serverUrl: '{{SERVER_URL}}'
        };

        async function sendChatMessage() {
            const token = localStorage.getItem('access_token');
            const model = document.getElementById('modelInput').value;
            const message = document.getElementById('messageInput').value;
            const responseDiv = document.getElementById('response');

            if (!token) {
                responseDiv.textContent = 'No token available. Please complete OAuth flow first.';
                responseDiv.className = 'error';
                return;
            }

            if (!message.trim()) {
                responseDiv.textContent = 'Please enter a message.';
                responseDiv.className = 'error';
                return;
            }

            try {
                responseDiv.textContent = 'Sending request...';
                responseDiv.className = 'info';

                const response = await fetch(`${CONFIG.serverUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [{ role: 'user', content: message }],
                        max_tokens: 150,
                        temperature: 0.7
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.choices && data.choices.length > 0) {
                        responseDiv.textContent = data.choices[0].message.content;
                        responseDiv.className = 'success';
                    } else {
                        responseDiv.textContent = 'No response content received';
                        responseDiv.className = 'error';
                    }
                } else {
                    const errorText = await response.text();
                    responseDiv.textContent = `Error: ${response.status} ${response.statusText} - ${errorText}`;
                    responseDiv.className = 'error';
                }
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
                responseDiv.className = 'error';
            }
        }

        function updateTokenStatus() {
            const token = localStorage.getItem('access_token');
            const statusSpan = document.getElementById('tokenStatus');
            
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const isExpired = Date.now() > payload.exp * 1000;
                    
                    statusSpan.textContent = isExpired ? 'EXPIRED' : 'VALID';
                    statusSpan.style.color = isExpired ? '#dc3545' : '#28a745';
                } catch (e) {
                    statusSpan.textContent = 'INVALID';
                    statusSpan.style.color = '#dc3545';
                }
            } else {
                statusSpan.textContent = 'NO TOKEN';
                statusSpan.style.color = '#dc3545';
            }
        }

        function clearStorage() {
            localStorage.clear();
            updateTokenStatus();
            document.getElementById('response').textContent = 'Storage cleared. No response yet...';
            document.getElementById('response').className = '';
        }

        function goBack() {
            window.location.href = 'index.html';
        }

        // Set default message for testing
        document.addEventListener('DOMContentLoaded', () => {
            updateTokenStatus();
            document.getElementById('messageInput').value = 'What day comes after Monday?';
        });
    </script>
</body>
</html>
